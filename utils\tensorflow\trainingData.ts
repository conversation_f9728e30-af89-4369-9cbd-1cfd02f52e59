interface TrainingExample {
  text: string;
  intent: number;
}

// Intent mapping
export const INTENTS = {
  UNKNOWN: 0,
  DESTINATION: 1,
  DURATION: 2,
  BUDGET: 3,
  ACTIVITY: 4,
} as const;

export function generateTrainingData(): TrainingExample[] {
  return [
    // **DESTINATION INTENT (All 11 Davao City Districts + Key Attractions)**
    { text: "I want to go to Poblacion", intent: INTENTS.DESTINATION },
    { text: "What can I see in Talomo?", intent: INTENTS.DESTINATION },
    { text: "Is Agdao worth visiting?", intent: INTENTS.DESTINATION },
    { text: "Best spots in Buhangin", intent: INTENTS.DESTINATION },
    { text: "Things to do in Bunawan", intent: INTENTS.DESTINATION },
    { text: "How to get to Paquibato?", intent: INTENTS.DESTINATION },
    { text: "Attractions in Toril", intent: INTENTS.DESTINATION },
    { text: "Is Tugbok far from downtown?", intent: INTENTS.DESTINATION },
    { text: "Where is Calinan?", intent: INTENTS.DESTINATION },
    { text: "What's in Baguio District?", intent: INTENTS.DESTINATION },
    { text: "Planning a trip to Marilog", intent: INTENTS.DESTINATION },
    { text: "How to visit Eden Nature Park?", intent: INTENTS.DESTINATION },
    { text: "Where is People's Park?", intent: INTENTS.DESTINATION },
    { text: "Best places in downtown Davao", intent: INTENTS.DESTINATION },
    { text: "How to get to Samal Island?", intent: INTENTS.DESTINATION },

    // **DURATION INTENT (Unchanged, already Davao-focused)**
    { text: "Staying in Davao for 3", intent: INTENTS.DURATION },
    { text: "Is 2 days enough for Davao?", intent: INTENTS.DURATION },
    { text: "Weekend trip to Davao", intent: INTENTS.DURATION },
    { text: "Planning a 5-day Davao vacation", intent: INTENTS.DURATION },
    { text: "How long to explore Davao?", intent: INTENTS.DURATION },
    { text: "One week in Davao", intent: INTENTS.DURATION },
    { text: "Day trip recommendations", intent: INTENTS.DURATION },
    { text: "Best itinerary for 4 days", intent: INTENTS.DURATION },
    { text: "Short stay in Davao", intent: INTENTS.DURATION },
    { text: "Long vacation in Davao", intent: INTENTS.DURATION },

    // **BUDGET INTENT (Unchanged, already Davao-focused)**
    { text: "Cheap eats in Davao", intent: INTENTS.BUDGET },
    { text: "Luxury hotels in Davao", intent: INTENTS.BUDGET },
    { text: "Budget-friendly stays", intent: INTENTS.BUDGET },
    { text: "Is Davao expensive?", intent: INTENTS.BUDGET },
    { text: "Affordable tours in Davao", intent: INTENTS.BUDGET },
    { text: "High-end restaurants in Davao", intent: INTENTS.BUDGET },
    { text: "Cost of taxi in Davao", intent: INTENTS.BUDGET },
    { text: "How much for durian?", intent: INTENTS.BUDGET },
    { text: "Price of a city tour", intent: INTENTS.BUDGET },
    { text: "Free attractions in Davao", intent: INTENTS.BUDGET },

    // **ACTIVITY INTENT (Now district-specific where relevant)**
    { text: "Hiking in Marilog", intent: INTENTS.ACTIVITY },
    { text: "Best museums in Poblacion", intent: INTENTS.ACTIVITY },
    { text: "Beaches near Talomo", intent: INTENTS.ACTIVITY },
    { text: "Where to try durian in Agdao?", intent: INTENTS.ACTIVITY },
    { text: "Nightlife in Buhangin", intent: INTENTS.ACTIVITY },
    { text: "Bunawan crocodile park visit", intent: INTENTS.ACTIVITY },
    { text: "Where to go shopping in Toril?", intent: INTENTS.ACTIVITY },
    { text: "Best place for coffee in Calinan", intent: INTENTS.ACTIVITY },
    { text: "Firefly watching in Baguio District", intent: INTENTS.ACTIVITY },
    { text: "Davao river cruise", intent: INTENTS.ACTIVITY },
    { text: "Where to see eagles in Tugbok?", intent: INTENTS.ACTIVITY },
    { text: "Best durian buffet in Paquibato", intent: INTENTS.ACTIVITY },
    { text: "Zip line in Eden Nature Park", intent: INTENTS.ACTIVITY },
    { text: "Cultural shows in Davao", intent: INTENTS.ACTIVITY },
    { text: "Where to try kinilaw in Poblacion?", intent: INTENTS.ACTIVITY }
  ];
}