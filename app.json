{"expo": {"name": "<PERSON><PERSON><PERSON>", "slug": "Davao Wanderer agent", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logos.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.codelab265.travelplannerai"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.codelab265.travelplanneraiagent", "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.INTERNET"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/logos.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#04132c"}], ["expo-file-system"]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/dfce7a4d-5cf6-4425-b6a3-348bab00fb2c"}}}