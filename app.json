{"expo": {"name": "Davao Wanderer Agent", "slug": "davao-wanderer-agent", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/logos.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.codelab265.travelplanneraiagent"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.codelab265.travelplanneraiagent", "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.INTERNET", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.INTERNET"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/logos.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#04132c"}], ["expo-file-system"]], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "c120988a-90d6-49e2-86a8-440b62e7cd3d"}}, "runtimeVersion": {"policy": "appVersion"}, "updates": {"url": "https://u.expo.dev/c120988a-90d6-49e2-86a8-440b62e7cd3d"}}}