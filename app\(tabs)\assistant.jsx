import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Alert,
  Linking,
  Image,
  Pressable,
} from "react-native";
import React, { useEffect, useState, useCallback, useRef } from "react";
import { GiftedChat, Message } from "react-native-gifted-chat";
import * as GoogleGenerativeAI from "@google/generative-ai";
import { Instructions } from "@/data/data";
import { useTravelPlans } from "@/contexts/TravelPlansContext";
import { useLocalSearchParams, useRouter } from "expo-router";
import { LinearGradient } from "expo-linear-gradient";
import { MaterialIcons } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

const Assistant = () => {
  const [messages, setMessages] = useState([]);
  const [chatSession, setChatSession] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const apiKey = "AIzaSyAKTETTW4T1PLvH3NMSfcZeE7vO6PG7D94";
  const { chatId } = useLocalSearchParams();
  const { addChat, updateChat, getChatById, addPlan } = useTravelPlans();
  const router = useRouter();
  const currentChatId = useRef(chatId);
  const saveTimeout = useRef(null);

  const initChat = useCallback(async () => {
    try {
      const genAI = new GoogleGenerativeAI.GoogleGenerativeAI(apiKey);
      const model = genAI.getGenerativeModel({
        model: "gemini-2.0-flash",
        systemInstruction: Instructions,
      });

      const generationConfig = {
        temperature: 1,
        topP: 0.95,
        topK: 40,
        maxOutputTokens: 9000,
      };

      const newChatSession = model.startChat({
        generationConfig,
        history: [],
      });

          if (!chatId) {
      const welcomeMessage = {
        text: "Hello! 👋 Welcome to Davao Wanderer, your local travel buddy. Ask me anything about Davao City, and I’ll help you plan the perfect trip! 🏝️🛵🗺️",
      };

      // Initialize the messages array with the welcome message
      setMessages([welcomeMessage]);
    }

      setChatSession(newChatSession);
      
    } catch (error) {
      console.error("Error initializing chat:", error);
      Alert.alert("Error", "Failed to initialize chat. Please try again.");
    }
  }, []);

  useEffect(() => {
    currentChatId.current = chatId;
    loadExistingChat();
    return () => {
      if (saveTimeout.current) {
        clearTimeout(saveTimeout.current);
      }
    };
  }, [chatId]);

  const loadExistingChat = async () => {
    try {
      if (chatId) {
        const existingChat = getChatById(chatId);
        if (existingChat) {
          setMessages(existingChat.messages || []);
        }
      }
      await initChat();
    } catch (error) {
      console.error("Error loading chat:", error);
      Alert.alert("Error", "Failed to load chat history. Please try again.");
    }
  };

  const saveChat = async (newMessages, userMessage, assistantMessage) => {
    if (isSaving) return;

    try {
      setIsSaving(true);
      if (saveTimeout.current) {
        clearTimeout(saveTimeout.current);
      }

      saveTimeout.current = setTimeout(async () => {
        if (currentChatId.current) {
          await updateChat(currentChatId.current, newMessages);
        } else {
          const newChatId = await addChat({
            title: userMessage.text,
            lastMessage: assistantMessage.text,
            messages: newMessages,
          });
          if (newChatId) {
            currentChatId.current = newChatId;
            router.setParams({ chatId: newChatId });
          }
        }
      }, 500);
    } catch (error) {
      console.error("Error saving chat:", error);
      Alert.alert("Error", "Failed to save chat. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  const extractResponseType = (text) => {
    try {
      // Check for JSON structure in the response
      const parts = text?.split(/```json|```/);
      if (parts?.length >= 3 && parts[1]?.trim()) {
        const jsonStr = parts[1]?.trim();
        const jsonResponse = JSON.parse(jsonStr || "{}");

        // If it has a type field, return it with the cleaned text
        if (jsonResponse?.type) {
          // Get the text without the JSON part
          const cleanedText = text?.replace(/```json[\s\S]*?```/, "").trim();
          return {
            type: jsonResponse?.type,
            data: jsonResponse || {},
            text: cleanedText || "",
          };
        }
      }
    } catch (error) {
      console.error("Error parsing response type:", error);
    }

    // Default to normal response if no JSON or parsing error
    return { type: "normal", text: text || "" };
  };

  const onSend = useCallback(
    async (newMessages = []) => {
      const userMessage = newMessages[0];
      if (!userMessage?.text?.trim()) return;

      try {
        const updatedMessages = GiftedChat.append(messages, newMessages);
        setMessages(updatedMessages);

        if (chatSession) {
          setIsTyping(true);
          const result = await chatSession.sendMessage(userMessage.text);
          const response = await result.response.text();
          console.log("Response:", response);

          const { type, text, data } = extractResponseType(response);

          const assistantMessage = {
            _id: Date.now().toString(),
            text: text,
            createdAt: new Date(),
            user: {
              _id: 2,
              name: "Assistant",
              avatar: "https://placehold.co/100x100/png",
            },
            responseType: type,
            responseData: data,
          };

          const newUpdatedMessages = GiftedChat.append(updatedMessages, [
            assistantMessage,
          ]);
          setMessages(newUpdatedMessages);

          // If it's a travel plan, save it to the travel plans
          if (type === "travel_plan" && data?.plan) {
            try {
              // Ensure the plan has an ID
              if (data.plan) {
                if (!data.plan.id) {
                  data.plan.id = Date.now().toString();
                } else {
                  // Ensure ID is a string
                  data.plan.id = String(data.plan.id);
                }

                console.log("Saving travel plan with ID:", data.plan.id);
                console.log(
                  "Plan data before saving:",
                  JSON.stringify(data.plan)
                );
                await addPlan(data.plan);
              }
            } catch (error) {
              console.error("Error saving travel plan:", error);
            }
          }

          await saveChat(newUpdatedMessages, userMessage, assistantMessage);
        }
      } catch (error) {
        console.error("Error in chat:", error);
        Alert.alert("Error", "Failed to send message. Please try again.");
      } finally {
        setIsTyping(false);
      }
    },
    [chatSession, messages]
  );

  const startNewConversation = async () => {
    try {
      setMessages([]);
      currentChatId.current = null;
      router.setParams({ chatId: null });
      await initChat();
    } catch (error) {
      console.error("Error starting new conversation:", error);
      Alert.alert(
        "Error",
        "Failed to start new conversation. Please try again."
      );
    }
  };

  const processMarkdown = (text) => {
    return text.replace(/\*\*(.*?)\*\*/g, "$1");
  };

  const handleLinkPress = async (url) => {
    try {
      // Clean and validate the URL
      if (!url) {
        console.error("Invalid URL:", url);
        Alert.alert("Error", "Invalid link format");
        return;
      }

      // Ensure the URL is properly encoded
      const encodedUrl = encodeURI(url);
      const supported = await Linking.canOpenURL(encodedUrl);

      if (supported) {
        await Linking.openURL(encodedUrl);
      } else {
        // Try opening in Google Maps app with a different format
        const coordinates = url.match(/query=([-\d.]+),([-\d.]+)/);
        if (coordinates) {
          const [_, lat, lng] = coordinates;
          const mapsUrl = `google.navigation:q=${lat},${lng}`;
          const canOpenMaps = await Linking.canOpenURL(mapsUrl);

          if (canOpenMaps) {
            await Linking.openURL(mapsUrl);
          } else {
            // Fallback to browser
            await Linking.openURL(encodedUrl);
          }
        } else {
          Alert.alert("Error", "Cannot open this link");
        }
      }
    } catch (error) {
      console.error("Error opening link:", error);
      Alert.alert("Error", "Failed to open link");
    }
  };

  const renderMessageText = (text) => {
    const parts = [];
    let lastIndex = 0;

    // Regex to find all markdown-style location links
    const locationRegex =
      /\[([^\]]+)\]\((https:\/\/(?:www\.)?(?:maps\.google\.com|google\.com\/maps)[^)]+)\)/g;

    let match;
    while ((match = locationRegex.exec(text)) !== null) {
      const [fullMatch, linkText, url] = match;

      // Add text before the match
      if (lastIndex < match.index) {
        const textBefore = text.substring(lastIndex, match.index);
        parts.push(
          <Text key={`text-${lastIndex}`}>
            {textBefore.split("**").map((part, i) => (
              <Text key={i} style={i % 2 === 1 ? styles.boldText : null}>
                {part}
              </Text>
            ))}
          </Text>
        );
      }

      // Add location icon button for each match
      parts.push(
        <TouchableOpacity
          key={`location-${match.index}`}
          onPress={() => Linking.openURL(url)}
          style={styles.locationButton}
        >
          <MaterialIcons name="place" size={16} color="#FFFFFF" />
        </TouchableOpacity>
      );

      lastIndex = match.index + fullMatch.length;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      const remainingText = text.substring(lastIndex);
      parts.push(
        <Text key={`text-end`}>
          {remainingText.split("**").map((part, i) => (
            <Text key={i} style={i % 2 === 1 ? styles.boldText : null}>
              {part}
            </Text>
          ))}
        </Text>
      );
    }

    return parts;
  };

  const TravelPlanCard = ({ plan, onPress }) => {
    return (
      <Pressable style={styles.travelPlanCard} onPress={onPress}>
        <LinearGradient
          colors={["#4c669f", "#3b5998", "#192f6a"]}
          style={styles.travelPlanCardGradient}
        >
          <View style={styles.travelPlanCardHeader}>
            <Text style={styles.travelPlanCardTitle}>{plan.destination}</Text>
            <Text style={styles.travelPlanCardSubtitle}>
              {plan.duration.days} Days, {plan.duration.nights} Nights •{" "}
              {plan.travelType}
            </Text>
          </View>

          <View style={styles.travelPlanCardContent}>
            <View style={styles.travelPlanCardSection}>
              <MaterialIcons name="hotel" size={18} color="#fff" />
              <Text style={styles.travelPlanCardSectionText}>
                {plan.accommodationSuggestions.length} Accommodations
              </Text>
            </View>

            <View style={styles.travelPlanCardSection}>
              <MaterialIcons name="restaurant" size={18} color="#fff" />
              <Text style={styles.travelPlanCardSectionText}>
                {plan.restaurantSuggestions.length} Restaurants
              </Text>
            </View>

            <View style={styles.travelPlanCardSection}>
              <MaterialIcons name="beach-access" size={18} color="#fff" />
              <Text style={styles.travelPlanCardSectionText}>
                {plan.beachSuggestions.length} Beaches
              </Text>
            </View>
          </View>

          <View style={styles.travelPlanCardFooter}>
            <Text style={styles.travelPlanCardBudget}>
              Budget: {plan.totalBudgetEstimate.min} -{" "}
              {plan.totalBudgetEstimate.max} {plan.totalBudgetEstimate.currency}
            </Text>
            <Text style={styles.travelPlanCardAction}>View Details</Text>
          </View>
        </LinearGradient>
      </Pressable>
    );
  };

  const handleViewPlanDetails = (plan) => {
    // Ensure plan has a valid ID before navigation
    if (plan.id) {
      plan.id = String(plan.id);
    }

    // Navigate to plan details with the plan data
    router.push({
      pathname: "/plan/[id]",
      params: { id: JSON.stringify(plan) },
    });
  };

  const renderTravelPlanMessage = (message) => {
    if (!message.responseData || !message.responseData.plan) {
      return null;
    }

    const plan = message.responseData.plan;
    // Ensure the plan ID is a string for consistency
    plan.id = String(plan.id);

    return (
      <View style={styles.travelPlanContainer}>
        <Text style={styles.travelPlanIntro}>{message.text}</Text>
        <TravelPlanCard
          plan={plan}
          onPress={() => {
            handleViewPlanDetails(plan);
          }}
        />
      </View>
    );
  };

  const renderMessage = useCallback((props) => {
    const message = props.currentMessage;
    if (!message) return null;

    if (message.user._id === 2) {
      // Check for response type
      const responseType = message.responseType || "normal";

      if (responseType === "travel_plan") {
        return (
          <View style={styles.messageBubbleContainer}>
            <View style={styles.assistantBubble}>
              {renderTravelPlanMessage(message)}
            </View>
          </View>
        );
      } else {
        // Normal message
        return (
          <View style={styles.messageBubbleContainer}>
            <View style={styles.assistantBubble}>
              <Text style={styles.messageText}>
                {renderMessageText(message.text)}
              </Text>
            </View>
          </View>
        );
      }
    }

    return <Message {...props} />;
  }, []);

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.newChatButton}
        onPress={startNewConversation}
      >
        <Text style={styles.buttonText}>New Conversation</Text>
      </TouchableOpacity>

      <GiftedChat
        messages={messages}
        onSend={(messages) => onSend(messages)}
        user={{
          _id: 1,
        }}
        isTyping={isTyping}
        renderAvatar={null}
        placeholder="Type your message here..."
        renderMessage={renderMessage}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  newChatButton: {
    backgroundColor: "#007AFF",
    padding: 10,
    margin: 10,
    borderRadius: 8,
    alignItems: "center",
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  messageBubbleContainer: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  assistantBubble: {
    backgroundColor: "#E8EAF7",
    padding: 10,
    borderRadius: 15,
    maxWidth: "90%",
    marginBottom: 10,
    alignSelf: "flex-start",
  },
  messageText: {
    fontSize: 16,
    color: "#333",
  },
  boldText: {
    fontWeight: "bold",
    color: "#007AFF",
    fontSize: 18,
  },
  link: {
    color: "red",
    textDecorationLine: "underline",
    fontWeight: "500",
  },
  locationButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: Colors.primary,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginVertical: 5,
    alignSelf: "flex-start",
  },
  locationButtonText: {
    color: "#FFFFFF",
    marginLeft: 6,
    fontSize: 14,
    fontWeight: "500",
  },
  // Travel Plan Card Styles
  travelPlanContainer: {
    width: "100%",
  },
  travelPlanIntro: {
    fontSize: 16,
    color: "#333",
    marginBottom: 10,
  },
  travelPlanCard: {
    marginVertical: 8,
    borderRadius: 15,
    overflow: "hidden",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  travelPlanCardGradient: {
    padding: 16,
  },
  travelPlanCardHeader: {
    marginBottom: 12,
  },
  travelPlanCardTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#fff",
  },
  travelPlanCardSubtitle: {
    fontSize: 14,
    color: "#e0e0e0",
    marginTop: 4,
  },
  travelPlanCardContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    flexWrap: "wrap",
    marginBottom: 12,
  },
  travelPlanCardSection: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 6,
    marginRight: 10,
  },
  travelPlanCardSectionText: {
    color: "#fff",
    marginLeft: 6,
    fontSize: 14,
  },
  travelPlanCardFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    borderTopWidth: 1,
    borderTopColor: "rgba(255,255,255,0.2)",
    paddingTop: 10,
    marginTop: 5,
  },
  travelPlanCardBudget: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "500",
  },
  travelPlanCardAction: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "bold",
    backgroundColor: "rgba(255,255,255,0.2)",
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 10,
  },
});

export default Assistant;
